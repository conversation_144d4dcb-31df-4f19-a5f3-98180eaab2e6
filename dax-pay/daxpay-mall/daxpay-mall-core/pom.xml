<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.dromara.daxpay</groupId>
        <artifactId>daxpay-mall</artifactId>
        <version>3.0.13</version>
    </parent>

    <artifactId>daxpay-mall-core</artifactId>
    <name>daxpay-mall-core</name>
    <description>DaxPay商城核心模块</description>

    <dependencies>
        <!-- DaxPay核心模块 -->
        <dependency>
            <groupId>org.dromara.daxpay</groupId>
            <artifactId>daxpay-open-core</artifactId>
        </dependency>

        <!-- BootX平台核心 -->
        <dependency>
            <groupId>cn.bootx.platform</groupId>
            <artifactId>bootx-platform-core</artifactId>
        </dependency>

        <!-- BootX平台MyBatis Plus基础 -->
        <dependency>
            <groupId>cn.bootx.platform</groupId>
            <artifactId>common-mybatis-plus</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- MyBatis Plus已通过common-mybatis-plus模块引入，无需重复依赖 -->

        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <version>3.4.3</version>
        </dependency>

        <!-- Swagger注解 -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-common</artifactId>
            <version>2.7.0</version>
        </dependency>

        <!-- MapStruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArgs>
                        <arg>-Amapstruct.suppressGeneratorTimestamp=true</arg>
                        <arg>-Amapstruct.suppressGeneratorVersionInfoComment=true</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>