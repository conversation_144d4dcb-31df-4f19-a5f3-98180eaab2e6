package org.dromara.daxpay.mall.convert;

import org.dromara.daxpay.mall.entity.MallCategory;
import org.dromara.daxpay.mall.param.MallCategorySaveParam;
import org.dromara.daxpay.mall.result.MallCategoryResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 商城商品分类转换器
 * <AUTHOR>
 * @since 2024/7/26
 */
@Mapper
public interface MallCategoryConvert {
    MallCategoryConvert CONVERT = Mappers.getMapper(MallCategoryConvert.class);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "level", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    MallCategory toEntity(MallCategorySaveParam param);

    @Mapping(target = "children", ignore = true)
    @Mapping(target = "productCount", ignore = true)
    MallCategoryResult toResult(MallCategory entity);
}