package org.dromara.daxpay.mall.convert;

import org.dromara.daxpay.mall.entity.MallProduct;
import org.dromara.daxpay.mall.param.MallProductSaveParam;
import org.dromara.daxpay.mall.result.MallProductResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 商城商品转换器
 * <AUTHOR>
 * @since 2024/7/26
 */
@Mapper
public interface MallProductConvert {
    MallProductConvert CONVERT = Mappers.getMapper(MallProductConvert.class);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "salesCount", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    MallProduct toEntity(MallProductSaveParam param);

    @Mapping(target = "categoryName", ignore = true)
    @Mapping(target = "lowStock", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MallProductResult toResult(MallProduct entity);
}