package org.dromara.daxpay.mall.convert;

import org.dromara.daxpay.mall.entity.MallCart;
import org.dromara.daxpay.mall.param.MallCartAddParam;
import org.dromara.daxpay.mall.result.MallCartResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 商城购物车转换器
 * <AUTHOR>
 * @since 2024/7/26
 */
@Mapper
public interface MallCartConvert {
    MallCartConvert CONVERT = Mappers.getMapper(MallCartConvert.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "selected", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    MallCart toEntity(MallCartAddParam param);

    @Mapping(target = "productName", ignore = true)
    @Mapping(target = "productImage", ignore = true)
    @Mapping(target = "productPrice", ignore = true)
    @Mapping(target = "subtotal", ignore = true)
    @Mapping(target = "inStock", ignore = true)
    @Mapping(target = "productStatus", ignore = true)
    @Mapping(target = "stock", ignore = true)
    @Mapping(target = "stockEnabled", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MallCartResult toResult(MallCart entity);
}