package org.dromara.daxpay.mall.convert;

import org.dromara.daxpay.mall.entity.MallOrder;
import org.dromara.daxpay.mall.entity.MallOrderItem;
import org.dromara.daxpay.mall.result.MallOrderItemResult;
import org.dromara.daxpay.mall.result.MallOrderResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 商城订单转换器
 * <AUTHOR>
 * @since 2024/7/26
 */
@Mapper
public interface MallOrderConvert {
    MallOrderConvert CONVERT = Mappers.getMapper(MallOrderConvert.class);

    @Mapping(target = "items", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MallOrderResult toResult(MallOrder entity);

    MallOrderItemResult toItemResult(MallOrderItem entity);
}