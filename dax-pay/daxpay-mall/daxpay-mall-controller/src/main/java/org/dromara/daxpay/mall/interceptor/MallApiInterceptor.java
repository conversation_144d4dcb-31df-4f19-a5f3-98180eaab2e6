package org.dromara.daxpay.mall.interceptor;

import cn.bootx.platform.core.rest.Res;
import cn.bootx.platform.core.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.daxpay.mall.config.MallRateLimitConfig;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

/**
 * 商城API拦截器
 * <AUTHOR>
 * @since 2024/7/26
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MallApiInterceptor implements HandlerInterceptor {

    private final MallRateLimitConfig.MallRateLimiter rateLimiter;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        
        long startTime = System.currentTimeMillis();
        request.setAttribute("startTime", startTime);
        
        String requestId = generateRequestId();
        request.setAttribute("requestId", requestId);
        
        // 记录请求日志
        logRequest(request, requestId);
        
        // 限流检查
        if (!checkRateLimit(request, response)) {
            return false;
        }
        
        // API监控
        monitorApiAccess(request);
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        
        long startTime = (Long) request.getAttribute("startTime");
        long duration = System.currentTimeMillis() - startTime;
        String requestId = (String) request.getAttribute("requestId");
        
        // 记录响应日志
        logResponse(request, response, requestId, duration, ex);
        
        // API性能监控
        monitorApiPerformance(request, duration, response.getStatus());
    }

    /**
     * 限流检查
     */
    private boolean checkRateLimit(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String uri = request.getRequestURI();
        
        // 跳过不需要限流的接口
        if (isSkipRateLimit(uri)) {
            return true;
        }
        
        String clientIp = getClientIp(request);

        // 根据用户类型设置不同的限流策略
        String rateLimitKey;
        int limit;
        int window = 60; // 1分钟窗口

        if (StpUtil.isLogin()) {
            String userId = StpUtil.getLoginIdAsString();
            rateLimitKey = "rate_limit:user:" + userId;

            // 管理员更高的限流阈值
            if (StpUtil.hasRole("admin")) {
                limit = 1000; // 管理员每分钟1000次
            } else {
                limit = 100; // 普通用户每分钟100次
            }
        } else {
            // 匿名用户按IP限流
            rateLimitKey = "rate_limit:ip:" + clientIp;
            limit = 50; // 匿名用户每分钟50次
        }
        
        if (!rateLimiter.isAllowed(rateLimitKey, limit, window)) {
            response.setStatus(429);
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            
            // 添加限流响应头
            response.setHeader("X-RateLimit-Limit", String.valueOf(limit));
            response.setHeader("X-RateLimit-Remaining", String.valueOf(rateLimiter.getRemainingCount(rateLimitKey, limit)));
            response.setHeader("X-RateLimit-Reset", String.valueOf(rateLimiter.getResetTime(rateLimitKey)));
            
            String jsonResponse = JsonUtil.toJsonStr(Res.response(429, "请求过于频繁，请稍后再试"));
            response.getWriter().write(jsonResponse);
            
            return false;
        }
        
        return true;
    }

    /**
     * 记录请求日志
     */
    private void logRequest(HttpServletRequest request, String requestId) {
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        String clientIp = getClientIp(request);
        String userAgent = request.getHeader("User-Agent");
        
        String userId = "anonymous";
        if (StpUtil.isLogin()) {
            userId = StpUtil.getLoginIdAsString();
        }
        
        log.info("API请求 [{}] {} {} {} - IP: {}, User: {}, UA: {}", 
                requestId, method, uri, 
                StringUtils.hasText(queryString) ? "?" + queryString : "",
                clientIp, userId, userAgent);
    }

    /**
     * 记录响应日志
     */
    private void logResponse(HttpServletRequest request, HttpServletResponse response, 
                           String requestId, long duration, Exception ex) {
        String method = request.getMethod();
        String uri = request.getRequestURI();
        int status = response.getStatus();
        
        if (ex != null) {
            log.error("API响应 [{}] {} {} - Status: {}, Duration: {}ms, Error: {}", 
                    requestId, method, uri, status, duration, ex.getMessage());
        } else {
            log.info("API响应 [{}] {} {} - Status: {}, Duration: {}ms", 
                    requestId, method, uri, status, duration);
        }
    }

    /**
     * API访问监控
     */
    private void monitorApiAccess(HttpServletRequest request) {
        // 这里可以集成监控系统，如Prometheus、Micrometer等
        // 记录API访问次数、用户分布等指标
        
        String uri = request.getRequestURI();
        String method = request.getMethod();
        
        // 示例：记录API访问计数
        log.debug("API访问监控: {} {}", method, uri);
    }

    /**
     * API性能监控
     */
    private void monitorApiPerformance(HttpServletRequest request, long duration, int status) {
        // 这里可以集成APM系统，记录API性能指标
        
        String uri = request.getRequestURI();
        String method = request.getMethod();
        
        // 慢接口告警
        if (duration > 5000) { // 超过5秒
            log.warn("慢接口告警: {} {} - Duration: {}ms, Status: {}", method, uri, duration, status);
        }
        
        // 错误接口告警
        if (status >= 500) {
            log.error("错误接口告警: {} {} - Status: {}, Duration: {}ms", method, uri, status, duration);
        }
    }

    /**
     * 是否跳过限流
     */
    private boolean isSkipRateLimit(String uri) {
        return uri.startsWith("/mall/api/health") ||
               uri.startsWith("/mall/api/ping") ||
               uri.startsWith("/swagger-ui") ||
               uri.startsWith("/v3/api-docs");
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }
}