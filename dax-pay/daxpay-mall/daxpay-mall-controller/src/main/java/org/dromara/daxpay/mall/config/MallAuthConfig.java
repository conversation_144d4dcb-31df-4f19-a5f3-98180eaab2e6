package org.dromara.daxpay.mall.config;

import cn.bootx.platform.starter.auth.service.RouterCheck;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Mall模块认证配置
 * 基于Sa-Token实现，与BootX平台保持一致
 * 
 * <AUTHOR>
 * @since 2024/8/3
 */
@Slf4j
@Component
public class MallAuthConfig implements RouterCheck {

    @Override
    public boolean check(Object handler) {
        String requestPath = cn.bootx.platform.common.spring.util.WebServletUtil.getPath();
        
        // 公开接口，无需认证
        if (isPublicPath(requestPath)) {
            return true;
        }
        
        // 用户相关接口需要认证
        if (isUserPath(requestPath)) {
            return StpUtil.isLogin();
        }
        
        // 管理接口需要管理员权限
        if (isAdminPath(requestPath)) {
            return StpUtil.isLogin() && StpUtil.hasRole("admin");
        }
        
        // 默认需要认证
        return StpUtil.isLogin();
    }

    @Override
    public int sortNo() {
        return 100; // 优先级
    }

    /**
     * 判断是否为公开路径
     */
    private boolean isPublicPath(String path) {
        return path.startsWith("/mall/api/") ||
               path.equals("/mall/user/register") ||
               path.equals("/mall/user/login") ||
               path.equals("/mall/user/send-code") ||
               path.equals("/mall/user/verify-code") ||
               path.equals("/mall/user/reset-password") ||
               path.startsWith("/mall/payment/callback/") ||
               path.startsWith("/mall/payment/notify/") ||
               path.startsWith("/swagger-ui/") ||
               path.startsWith("/v3/api-docs/") ||
               path.startsWith("/swagger-resources/") ||
               path.startsWith("/webjars/") ||
               path.equals("/mall/product/page") ||
               path.matches("/mall/product/\\d+") ||
               path.equals("/mall/product/search") ||
               path.startsWith("/mall/product/category/") ||
               path.startsWith("/mall/category/");
    }

    /**
     * 判断是否为用户路径
     */
    private boolean isUserPath(String path) {
        return path.startsWith("/mall/user/") ||
               path.startsWith("/mall/cart/") ||
               path.startsWith("/mall/order/") ||
               path.startsWith("/mall/payment/");
    }

    /**
     * 判断是否为管理员路径
     */
    private boolean isAdminPath(String path) {
        return path.startsWith("/mall/alipay-isv/") ||
               path.startsWith("/mall/statistics/") ||
               path.startsWith("/mall/config/");
    }
}
