package org.dromara.daxpay.mall.controller;

import org.dromara.daxpay.core.result.DaxResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.daxpay.mall.param.AlipaySubMerchantCreateParam;
import org.dromara.daxpay.mall.result.AlipaySubMerchantResult;
import org.dromara.daxpay.mall.service.AlipaySubMerchantService;
import cn.dev33.satoken.annotation.SaCheckRole;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 支付宝服务商管理控制器
 * <AUTHOR>
 * @since 2024/7/30
 */
@Slf4j
@Tag(name = "支付宝服务商管理")
@RestController
@RequestMapping("/mall/alipay-isv")
@RequiredArgsConstructor
public class AlipayIsvController {

    private final AlipaySubMerchantService subMerchantService;

    @Operation(summary = "创建子商户")
    @PostMapping("/sub-merchant")
    @SaCheckRole("admin")
    public DaxResult<AlipaySubMerchantResult> createSubMerchant(@RequestBody @Validated AlipaySubMerchantCreateParam param) {
        return DaxResult.ok(subMerchantService.createSubMerchant(param));
    }

    @Operation(summary = "获取子商户信息")
    @GetMapping("/sub-merchant/{merchantId}")
    @SaCheckRole("admin")
    public DaxResult<AlipaySubMerchantResult> getSubMerchant(@PathVariable Long merchantId) {
        return DaxResult.ok(subMerchantService.getSubMerchant(merchantId));
    }

    @Operation(summary = "更新子商户信息")
    @PutMapping("/sub-merchant/{subMerchantId}")
    @SaCheckRole("admin")
    public DaxResult<Void> updateSubMerchant(@PathVariable Long subMerchantId,
                                           @RequestBody @Validated AlipaySubMerchantCreateParam param) {
        subMerchantService.updateSubMerchant(subMerchantId, param);
        return DaxResult.ok();
    }

    @Operation(summary = "获取授权链接")
    @GetMapping("/sub-merchant/{subMerchantId}/auth-url")
    @SaCheckRole("admin")
    public DaxResult<String> getAuthUrl(@PathVariable Long subMerchantId) {
        return DaxResult.ok(subMerchantService.getAuthUrl(subMerchantId));
    }

    @Operation(summary = "处理授权回调")
    @PostMapping("/auth-callback")
    public DaxResult<Void> handleAuthCallback(@RequestParam String authCode,@RequestParam Long subMerchantId) {
        subMerchantService.handleAuthCallback(authCode, subMerchantId);
        return DaxResult.ok();
    }
}