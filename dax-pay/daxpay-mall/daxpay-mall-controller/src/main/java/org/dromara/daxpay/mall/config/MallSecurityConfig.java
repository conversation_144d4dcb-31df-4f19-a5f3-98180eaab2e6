package org.dromara.daxpay.mall.config;

import lombok.RequiredArgsConstructor;
import org.dromara.daxpay.mall.security.MallAuthenticationEntryPoint;
import org.dromara.daxpay.mall.security.MallJwtAuthenticationFilter;
import org.dromara.daxpay.mall.security.MallAccessDeniedHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * 商城安全配置
 * <AUTHOR>
 * @since 2024/7/26
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class MallSecurityConfig {

    private final MallJwtAuthenticationFilter jwtAuthenticationFilter;
    private final MallAuthenticationEntryPoint authenticationEntryPoint;
    private final MallAccessDeniedHandler accessDeniedHandler;

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 认证管理器
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    /**
     * 安全过滤器链
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF
            .csrf(csrf -> csrf.disable())

            // 强制HTTPS
            .requiresChannel(channel ->
                channel.requestMatchers(r -> r.getHeader("X-Forwarded-Proto") != null)
                       .requiresSecure())

            // 配置CORS
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))

            // 配置会话管理
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS))

            // 配置异常处理
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(authenticationEntryPoint)
                .accessDeniedHandler(accessDeniedHandler))
            
            // 配置请求授权
            .authorizeHttpRequests(authz -> authz
                // 公开接口
                .requestMatchers("/mall/api/**").permitAll()
                .requestMatchers("/mall/user/register").permitAll()
                .requestMatchers("/mall/user/login").permitAll()
                .requestMatchers("/mall/user/send-code").permitAll()
                .requestMatchers("/mall/user/verify-code").permitAll()
                .requestMatchers("/mall/user/reset-password").permitAll()
                .requestMatchers("/mall/payment/callback/**").permitAll()
                .requestMatchers("/mall/payment/notify/**").permitAll()
                
                // Swagger文档接口
                .requestMatchers("/swagger-ui/**").permitAll()
                .requestMatchers("/v3/api-docs/**").permitAll()
                .requestMatchers("/swagger-resources/**").permitAll()
                .requestMatchers("/webjars/**").permitAll()
                
                // 商品和分类查询接口（可匿名访问）
                .requestMatchers("/mall/product/page").permitAll()
                .requestMatchers("/mall/product/{id}").permitAll()
                .requestMatchers("/mall/product/search").permitAll()
                .requestMatchers("/mall/product/category/**").permitAll()
                .requestMatchers("/mall/category/**").permitAll()
                
                // 用户相关接口需要认证
                .requestMatchers("/mall/user/**").authenticated()
                .requestMatchers("/mall/cart/**").authenticated()
                .requestMatchers("/mall/order/**").authenticated()
                .requestMatchers("/mall/payment/**").authenticated()
                
                // 管理接口需要管理员权限
                .requestMatchers("/mall/alipay-isv/**").hasRole("ADMIN")
                .requestMatchers("/mall/statistics/**").hasRole("ADMIN")
                .requestMatchers("/mall/config/**").hasRole("ADMIN")
                
                // 其他请求需要认证
                .anyRequest().authenticated()
            )
            
            // 添加JWT过滤器
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * CORS配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许的源
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        
        // 允许的方法
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        
        // 允许的头
        configuration.setAllowedHeaders(Arrays.asList("*"));
        
        // 允许凭证
        configuration.setAllowCredentials(true);
        
        // 预检请求缓存时间
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}