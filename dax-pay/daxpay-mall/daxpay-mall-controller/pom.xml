<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.dromara.daxpay</groupId>
        <artifactId>daxpay-mall</artifactId>
        <version>3.0.13</version>
    </parent>

    <artifactId>daxpay-mall-controller</artifactId>
    <name>daxpay-mall-controller</name>
    <description>DaxPay商城控制器模块</description>

    <dependencies>
        <!-- Mall核心模块 - 包含param和result类 -->
        <dependency>
            <groupId>org.dromara.daxpay</groupId>
            <artifactId>daxpay-mall-core</artifactId>
        </dependency>
        
        <!-- Mall服务模块 -->
        <dependency>
            <groupId>org.dromara.daxpay</groupId>
            <artifactId>daxpay-mall-service</artifactId>
        </dependency>
        
        <!-- DaxPay开放控制器模块 -->
        <dependency>
            <groupId>org.dromara.daxpay</groupId>
            <artifactId>daxpay-open-controller</artifactId>
        </dependency>
        
        <!-- Web框架 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- BootX平台核心 -->
        <dependency>
            <groupId>cn.bootx.platform</groupId>
            <artifactId>bootx-platform-core</artifactId>
            <version>${bootx-platform.version}</version>
        </dependency>
        
        <!-- BootX平台认证模块 - 使用Sa-Token替代Spring Security -->
        <dependency>
            <groupId>cn.bootx.platform</groupId>
            <artifactId>starter-auth</artifactId>
            <version>${bootx-platform.version}</version>
        </dependency>
    </dependencies>

</project>