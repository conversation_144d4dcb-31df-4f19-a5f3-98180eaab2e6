package cn.bootx.platform.starter.file.convert;

import cn.bootx.platform.starter.file.entity.UploadFileInfo;
import cn.bootx.platform.starter.file.result.UploadFileResult;
import org.dromara.x.file.storage.core.FileInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/1/12
 */
@Mapper
public interface FileConvert {

    FileConvert CONVERT = Mappers.getMapper(FileConvert.class);

    @Mapping(target = "platformName", ignore = true)
    @Mapping(target = "metadata", ignore = true)
    @Mapping(target = "userMetadata", ignore = true)
    @Mapping(target = "thMetadata", ignore = true)
    @Mapping(target = "thUserMetadata", ignore = true)
    @Mapping(target = "attr", ignore = true)
    @Mapping(target = "fileAcl", ignore = true)
    @Mapping(target = "thFileAcl", ignore = true)
    UploadFileResult convert(UploadFileInfo in);

    @Mapping(target = "transMap", ignore = true)
    UploadFileInfo convert(FileInfo in);

    @Mapping(target = "metadata", ignore = true)
    @Mapping(target = "userMetadata", ignore = true)
    @Mapping(target = "thMetadata", ignore = true)
    @Mapping(target = "thUserMetadata", ignore = true)
    @Mapping(target = "attr", ignore = true)
    @Mapping(target = "fileAcl", ignore = true)
    @Mapping(target = "thFileAcl", ignore = true)
    @Mapping(target = "hashInfo", ignore = true)
    @Mapping(target = "uploadId", ignore = true)
    @Mapping(target = "uploadStatus", ignore = true)
    FileInfo toFileInfo(UploadFileInfo in);

    @Mapping(target = "platformName", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    UploadFileResult toResult(FileInfo in);

}
