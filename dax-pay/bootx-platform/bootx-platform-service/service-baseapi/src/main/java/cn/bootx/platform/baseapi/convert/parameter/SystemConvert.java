package cn.bootx.platform.baseapi.convert.parameter;

import cn.bootx.platform.baseapi.entity.parameter.SystemParameter;
import cn.bootx.platform.baseapi.param.parameter.SystemParameterParam;
import cn.bootx.platform.baseapi.result.parameter.SystemParameterResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 系统参数和系统配置实体类转换
 *
 * <AUTHOR>
 * @since 2021/10/25
 */
@Mapper
public interface SystemConvert {

    SystemConvert CONVERT = Mappers.getMapper(SystemConvert.class);

    SystemParameterResult convert(SystemParameter in);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    SystemParameter convert(SystemParameterParam in);

}
