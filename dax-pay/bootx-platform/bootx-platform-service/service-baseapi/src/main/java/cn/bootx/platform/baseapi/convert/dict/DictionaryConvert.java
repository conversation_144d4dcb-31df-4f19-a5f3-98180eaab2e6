package cn.bootx.platform.baseapi.convert.dict;

import cn.bootx.platform.baseapi.entity.dict.Dictionary;
import cn.bootx.platform.baseapi.entity.dict.DictionaryItem;
import cn.bootx.platform.baseapi.param.dict.DictionaryItemParam;
import cn.bootx.platform.baseapi.param.dict.DictionaryParam;
import cn.bootx.platform.baseapi.result.dict.DictionaryItemResult;
import cn.bootx.platform.baseapi.result.dict.DictionaryResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 字典转换
 *
 * <AUTHOR>
 * @since 2021/7/6
 */
@Mapper
public interface DictionaryConvert {

    DictionaryConvert CONVERT = Mappers.getMapper(DictionaryConvert.class);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    Dictionary convert(DictionaryParam in);

    DictionaryResult convert(Dictionary in);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    DictionaryItem convert(DictionaryItemParam in);

    DictionaryItemResult convert(DictionaryItem in);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    DictionaryItem convertSimple(DictionaryItemParam in);

}
