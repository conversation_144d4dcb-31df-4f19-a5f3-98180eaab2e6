package org.dromara.daxpay.channel.usdt.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * USDT通道配置实体
 * <AUTHOR>
 * @since 2024/7/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class UsdtChannelConfig extends BaseEntity {

    /** 商户号 */
    private String mchNo;
    
    /** 应用号 */
    private String appId;
    
    /** 网络类型 ERC20/TRC20/BEP20 */
    private String networkType;
    
    /** 节点端点列表 JSON格式 */
    private String nodeEndpoints;
    
    /** 合约地址 (ERC-20和BEP-20需要) */
    private String contractAddress;
    
    /** 确认区块数 */
    private Integer confirmationBlocks;
    
    /** 最小金额 */
    private BigDecimal minAmount;
    
    /** 最大金额 */
    private BigDecimal maxAmount;
    
    /** 是否启用 */
    private Boolean enabled;
    
    /** 热钱包地址 */
    private String hotWalletAddress;
    
    /** 冷钱包地址 */
    private String coldWalletAddress;
    
    /** 热钱包阈值 */
    private BigDecimal hotWalletThreshold;
    
    /** 加密的助记词 */
    private String encryptedMnemonic;
    
    /** 派生索引 */
    private Integer derivationIndex;
}