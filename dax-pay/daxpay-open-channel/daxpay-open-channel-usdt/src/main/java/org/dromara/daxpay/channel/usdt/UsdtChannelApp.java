package org.dromara.daxpay.channel.usdt;

import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * USDT支付通道实现
 * <AUTHOR>
 * @since 2024/7/17
 */
@ConfigurationPropertiesScan
@MapperScan(basePackages = "org.dromara.daxpay.channel.usdt.dao", annotationClass = Mapper.class)
@ComponentScan
public class UsdtChannelApp {
}
