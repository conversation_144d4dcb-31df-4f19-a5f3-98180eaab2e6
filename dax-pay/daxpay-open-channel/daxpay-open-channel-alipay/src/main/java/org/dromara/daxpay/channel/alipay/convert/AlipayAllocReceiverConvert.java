package org.dromara.daxpay.channel.alipay.convert;

import org.dromara.daxpay.channel.alipay.entity.allocation.AlipayAllocReceiver;
import org.dromara.daxpay.channel.alipay.param.allocation.AlipayAllocReceiverParam;
import org.dromara.daxpay.channel.alipay.result.allocation.AlipayAllocReceiverResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @since 2025/1/26
 */
@Mapper
public interface AlipayAllocReceiverConvert {
    AlipayAllocReceiverConvert CONVERT = Mappers.getMapper(AlipayAllocReceiverConvert.class);

    AlipayAllocReceiver copy(AlipayAllocReceiver aliAllocReceiver);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "receiverNo", ignore = true)
    AlipayAllocReceiver toEntity(AlipayAllocReceiverParam alipayAllocReceiverParam);

    @Mapping(target = "receiverNo", ignore = true)
    AlipayAllocReceiverResult toResult(AlipayAllocReceiver aliAllocReceiver);
}
