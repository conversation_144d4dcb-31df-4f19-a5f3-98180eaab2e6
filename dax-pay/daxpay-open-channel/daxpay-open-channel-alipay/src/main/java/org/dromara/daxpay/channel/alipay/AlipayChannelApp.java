package org.dromara.daxpay.channel.alipay;

import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * 支付宝支付实现
 * <AUTHOR>
 * @since 2024/6/25
 */
@ConfigurationPropertiesScan
@MapperScan(basePackages = "org.dromara.daxpay.channel.alipay.dao", annotationClass = Mapper.class)
@ComponentScan
public class AlipayChannelApp {
}
