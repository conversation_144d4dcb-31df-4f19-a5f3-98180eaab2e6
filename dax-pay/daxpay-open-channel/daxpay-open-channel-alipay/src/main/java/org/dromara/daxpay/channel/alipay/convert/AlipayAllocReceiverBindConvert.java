package org.dromara.daxpay.channel.alipay.convert;

import org.dromara.daxpay.channel.alipay.entity.allocation.AlipayAllocReceiverBind;
import org.dromara.daxpay.channel.alipay.param.allocation.AlipayAllocReceiverBindParam;
import org.dromara.daxpay.channel.alipay.result.allocation.AlipayAllocReceiverBindResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @since 2025/1/27
 */
@Mapper
public interface AlipayAllocReceiverBindConvert {
    AlipayAllocReceiverBindConvert CONVERT = Mappers.getMapper(AlipayAllocReceiverBindConvert.class);

    @Mapping(target = "mchName", ignore = true)
    @Mapping(target = "appName", ignore = true)
    AlipayAllocReceiverBindResult toResult(AlipayAllocReceiverBind alipayAllocReceiverBind);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "mchNo", ignore = true)
    @Mapping(target = "bind", ignore = true)
    @Mapping(target = "errorMsg", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    AlipayAllocReceiverBind toEntity(AlipayAllocReceiverBindParam param);
}
