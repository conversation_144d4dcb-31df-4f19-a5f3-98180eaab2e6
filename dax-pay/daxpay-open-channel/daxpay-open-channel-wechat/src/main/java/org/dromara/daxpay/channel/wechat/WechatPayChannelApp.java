package org.dromara.daxpay.channel.wechat;

import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * 微信官方商户支付实现
 * <AUTHOR>
 * @since 2024/7/16
 */
@ConfigurationPropertiesScan
@MapperScan(basePackages = "org.dromara.daxpay.channel.wechat.dao", annotationClass = Mapper.class)
@ComponentScan
public class WechatPayChannelApp {
}
