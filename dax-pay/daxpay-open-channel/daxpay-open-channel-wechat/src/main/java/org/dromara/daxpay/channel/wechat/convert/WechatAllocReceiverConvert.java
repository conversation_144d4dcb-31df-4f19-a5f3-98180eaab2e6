package org.dromara.daxpay.channel.wechat.convert;

import org.dromara.daxpay.channel.wechat.entity.allocation.WechatAllocReceiver;
import org.dromara.daxpay.channel.wechat.param.allocation.WechatAllocReceiverParam;
import org.dromara.daxpay.channel.wechat.result.allocation.WechatAllocReceiverResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @since 2025/1/26
 */
@Mapper
public interface WechatAllocReceiverConvert {
    WechatAllocReceiverConvert CONVERT = Mappers.getMapper(WechatAllocReceiverConvert.class);

    WechatAllocReceiver copy(WechatAllocReceiver wechatAllocReceiver);

    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "mchName", ignore = true)
    @Mapping(target = "appName", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    WechatAllocReceiverResult toResult(WechatAllocReceiver wechatAllocReceiver);

    @Mapping(target = "receiverNo", ignore = true)
    WechatAllocReceiver toEntity(WechatAllocReceiverParam param);
}
