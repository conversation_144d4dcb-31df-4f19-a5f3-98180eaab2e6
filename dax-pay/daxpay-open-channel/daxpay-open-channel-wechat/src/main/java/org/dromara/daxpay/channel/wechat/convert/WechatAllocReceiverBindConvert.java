package org.dromara.daxpay.channel.wechat.convert;

import org.dromara.daxpay.channel.wechat.entity.allocation.WechatAllocReceiverBind;
import org.dromara.daxpay.channel.wechat.param.allocation.WechatAllocReceiverBindParam;
import org.dromara.daxpay.channel.wechat.result.allocation.WechatAllocReceiverBindResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @since 2025/1/27
 */
@Mapper
public interface WechatAllocReceiverBindConvert {
    WechatAllocReceiverBindConvert CONVERT = Mappers.getMapper(WechatAllocReceiverBindConvert.class);

    @Mapping(target = "mchName", ignore = true)
    @Mapping(target = "appName", ignore = true)
    WechatAllocReceiverBindResult toResult(WechatAllocReceiverBind wxReceiver);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "mchNo", ignore = true)
    @Mapping(target = "errorMsg", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    WechatAllocReceiverBind toEntity(WechatAllocReceiverBindParam wxReceiver);
}
