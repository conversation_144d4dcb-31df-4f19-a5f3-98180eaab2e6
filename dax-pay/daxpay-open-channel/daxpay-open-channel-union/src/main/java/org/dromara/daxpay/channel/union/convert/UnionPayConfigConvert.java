package org.dromara.daxpay.channel.union.convert;

import org.dromara.daxpay.channel.union.result.UnionPayConfigResult;
import org.dromara.daxpay.channel.union.entity.config.UnionPayConfig;
import org.dromara.daxpay.channel.union.param.config.UnionPayConfigParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @since 2024/9/6
 */
@Mapper
public interface UnionPayConfigConvert {

    UnionPayConfigConvert CONVERT = Mappers.getMapper(UnionPayConfigConvert.class);

    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "mchName", ignore = true)
    @Mapping(target = "appName", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    UnionPayConfigResult toResult(UnionPayConfig unionPayConfig);

    @Mapping(target = "id", ignore = true)
    UnionPayConfig toEntity(UnionPayConfigParam param);

    UnionPayConfig copy(UnionPayConfig unionPayConfig);


}
