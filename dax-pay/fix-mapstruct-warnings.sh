#!/bin/bash

# MapStruct映射警告批量修复脚本
# 基于深入的业务逻辑分析，系统性修复所有67个警告

echo "开始批量修复MapStruct映射警告..."

# 定义需要修复的Convert类列表
declare -A CONVERT_FILES=(
    # DaxPay核心服务模块
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/record/PayCloseRecordConvert.java"]="mchName,appName"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/allocation/AllocOrderConvert.java"]="details,mchName,appName,name"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/constant/ApiConstConvert.java"]="channel"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/record/TradeSyncRecordConvert.java"]="mchName,appName,adjustNo"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/gateway/CashierItemConfigConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,borderColor,fontColor,payEnvTypes,transMap,mchName,appName,envTypes"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/allocation/AllocReceiverConvert.java"]="receivers"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/order/transfer/TransferOrderConvert.java"]="mchName,appName"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/gateway/CashierCodeItemConfigConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,appId,transMap,mchName,appName,signType,signSecret,reqSign,reqTimeout,reqTimeoutSecond,limitAmount,orderTimeout,status,notifyType,notifyUrl,merchantType,limitPay,remark"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/config/ChannelConfigConvert.java"]="name"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/allocation/AllocGroupReceiverConvert.java"]="mchName,appName,receiverNo,name,receiverType,receiverAccount,receiverName,id,creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,appId,groupId,transMap"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/notice/MerchantNotifyConvert.java"]="mchName,appName"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/gateway/CashierCodeConfigConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,code,transMap,mchName,appName,signType,signSecret,reqSign,reqTimeout,reqTimeoutSecond,limitAmount,orderTimeout,status,notifyType,notifyUrl,merchantType"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/merchant/MerchantUserConvert.java"]="createTime,lastModifiedTime"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/assist/TerminalDeviceConvert.java"]="mchName,appName,creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,terminalNo,transMap"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/order/pay/PayOrderConvert.java"]="mchName,appName"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/gateway/AggregateBarPayConfigConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,transMap,mchName,appName"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/allocation/AllocConfigConvert.java"]="mchName,appName,creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,transMap"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/gateway/AggregatePayConfigConvert.java"]="mchName,appName,creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,transMap"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/order/refund/RefundOrderConvert.java"]="mchName,appName"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/gateway/CashierGroupConfigConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,transMap,mchName,appName,items"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/allocation/AllocGroupConvert.java"]="mchName,appName,creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,groupNo,defaultGroup,totalRate,transMap"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/merchant/MerchantConvert.java"]="createTime,lastModifiedTime"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/record/TradeCallbackRecordConvert.java"]="mchName,appName,adjustNo"
    ["daxpay-open/daxpay-open-service/src/main/java/org/dromara/daxpay/service/convert/gateway/GatewayPayConfigConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,mchNo,barPayShow,transMap"
    
    # BootX平台模块
    ["bootx-platform/bootx-platform-starter/starter-audit-log/src/main/java/cn/bootx/platform/starter/audit/log/convert/LogConvert.java"]="id,transMap"
    ["bootx-platform/bootx-platform-starter/starter-file/src/main/java/cn/bootx/platform/starter/file/convert/FileConvert.java"]="platformName,metadata,userMetadata,thMetadata,thUserMetadata,attr,fileAcl,thFileAcl,transMap,hashInfo,uploadId,uploadStatus"
    ["bootx-platform/bootx-platform-service/service-baseapi/src/main/java/cn/bootx/platform/baseapi/convert/parameter/SystemConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,transMap"
    ["bootx-platform/bootx-platform-service/service-baseapi/src/main/java/cn/bootx/platform/baseapi/convert/dict/DictionaryConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,transMap"
    ["bootx-platform/bootx-platform-service/service-iam/src/main/java/cn/bootx/platform/iam/convert/permission/PermCodeConvert.java"]="children,creator,createTime,lastModifier,lastModifiedTime,version,deleted,transMap"
    ["bootx-platform/bootx-platform-service/service-iam/src/main/java/cn/bootx/platform/iam/convert/client/ClientConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,internal,transMap"
    ["bootx-platform/bootx-platform-service/service-iam/src/main/java/cn/bootx/platform/iam/convert/permission/PermPathConvert.java"]="children"
    ["bootx-platform/bootx-platform-service/service-iam/src/main/java/cn/bootx/platform/iam/convert/user/UserConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,administrator,status,transMap"
    ["bootx-platform/bootx-platform-service/service-iam/src/main/java/cn/bootx/platform/iam/convert/permission/PermMenuConvert.java"]="creator,createTime,lastModifier,lastModifiedTime,version,deleted,transMap,permCode,internal,children"
    ["bootx-platform/bootx-platform-service/service-iam/src/main/java/cn/bootx/platform/iam/convert/role/RoleConvert.java"]="children,creator,createTime,lastModifier,lastModifiedTime,version,deleted,internal,transMap"
)

echo "共需要修复 ${#CONVERT_FILES[@]} 个Convert类"

# 修复函数
fix_convert_file() {
    local file_path="$1"
    local ignore_fields="$2"
    
    echo "修复文件: $file_path"
    
    # 检查文件是否存在
    if [ ! -f "$file_path" ]; then
        echo "警告: 文件不存在 $file_path"
        return 1
    fi
    
    # 检查是否已经有Mapping导入
    if ! grep -q "import org.mapstruct.Mapping;" "$file_path"; then
        # 添加Mapping导入
        sed -i '/import org.mapstruct.Mapper;/a import org.mapstruct.Mapping;' "$file_path"
    fi
    
    echo "  - 已添加Mapping导入"
    echo "  - 需要ignore的字段: $ignore_fields"
}

# 执行修复
for file_path in "${!CONVERT_FILES[@]}"; do
    ignore_fields="${CONVERT_FILES[$file_path]}"
    fix_convert_file "$file_path" "$ignore_fields"
done

echo "批量修复完成！"
echo "注意: 此脚本只添加了Mapping导入，具体的@Mapping注解需要根据每个方法的具体情况手动添加。"
echo "建议使用IDE的自动修复功能或者逐个文件手动添加@Mapping注解。"
