package org.dromara.daxpay.service.convert.record;

import org.dromara.daxpay.service.entity.record.sync.TradeSyncRecord;
import org.dromara.daxpay.service.result.record.sync.TradeSyncRecordResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 支付同步记录同步
 * <AUTHOR>
 * @since 2023/7/14
 */
@Mapper
public interface TradeSyncRecordConvert {
    TradeSyncRecordConvert CONVERT = Mappers.getMapper(TradeSyncRecordConvert.class);

    @Mapping(target = "mchName", ignore = true)
    @Mapping(target = "appName", ignore = true)
    @Mapping(target = "adjustNo", ignore = true)
    TradeSyncRecordResult convert(TradeSyncRecord in);

}
