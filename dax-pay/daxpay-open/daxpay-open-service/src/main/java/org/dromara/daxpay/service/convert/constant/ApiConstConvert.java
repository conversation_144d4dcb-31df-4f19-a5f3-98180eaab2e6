package org.dromara.daxpay.service.convert.constant;

import org.dromara.daxpay.service.entity.constant.ApiConst;
import org.dromara.daxpay.service.result.constant.ApiConstResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @since 2024/7/14
 */
@Mapper
public interface ApiConstConvert {
    ApiConstConvert CONVERT = Mappers.getMapper(ApiConstConvert.class);

    @Mapping(target = "channel", ignore = true)
    ApiConstResult toResult(ApiConst source);
}
