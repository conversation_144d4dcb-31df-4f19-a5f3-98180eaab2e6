package org.dromara.daxpay.service.convert.merchant;

import org.dromara.daxpay.service.entity.merchant.MchApp;
import org.dromara.daxpay.service.param.merchant.MchAppParam;
import org.dromara.daxpay.service.result.merchant.MchAppResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @since 2024/6/24
 */
@Mapper
public interface MchAppConvert {
    MchAppConvert CONVERT = Mappers.getMapper(MchAppConvert.class);

    @Mapping(target = "mchName", ignore = true)
    MchAppResult toResult(MchApp entity);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "mchNo", ignore = true)
    @Mapping(target = "appId", ignore = true)
    @Mapping(target = "merchantType", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    MchApp toEntity(MchAppParam param);
}
