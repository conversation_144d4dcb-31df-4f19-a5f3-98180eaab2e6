package org.dromara.daxpay.service;

import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * 支付业务实现层
 * <AUTHOR>
 * @since 2024/5/23
 */
@ConfigurationPropertiesScan
@MapperScan(basePackages = "org.dromara.daxpay.service.dao", annotationClass = Mapper.class)
@ComponentScan
public class DaxpayPayServiceApp {
}
