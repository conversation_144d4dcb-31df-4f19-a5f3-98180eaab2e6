package org.dromara.daxpay.service.convert.record;

import org.dromara.daxpay.service.entity.record.flow.TradeFlowRecord;
import org.dromara.daxpay.service.result.record.flow.TradeFlowRecordResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @since 2024/4/21
 */
@Mapper
public interface TradeFlowRecordConvert {
    TradeFlowRecordConvert CONVERT = Mappers.getMapper(TradeFlowRecordConvert.class);

    @Mapping(target = "mchName", ignore = true)
    @Mapping(target = "appName", ignore = true)
    TradeFlowRecordResult convert(TradeFlowRecord entity);
}
