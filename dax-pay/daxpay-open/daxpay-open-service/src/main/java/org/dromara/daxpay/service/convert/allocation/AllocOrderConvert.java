package org.dromara.daxpay.service.convert.allocation;

import org.dromara.daxpay.core.result.allocation.order.AllocDetailResult;
import org.dromara.daxpay.core.result.allocation.order.AllocOrderResult;
import org.dromara.daxpay.service.entity.allocation.order.AllocDetail;
import org.dromara.daxpay.service.entity.allocation.order.AllocOrder;
import org.dromara.daxpay.service.result.allocation.order.AllocDetailVo;
import org.dromara.daxpay.service.result.allocation.order.AllocOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024/11/15
 */
@Mapper
public interface AllocOrderConvert {
    AllocOrderConvert CONVERT = Mappers.getMapper(AllocOrderConvert.class);

    @Mapping(target = "details", ignore = true)
    AllocOrderResult toResult(AllocOrder in);

    AllocDetailResult toResult(AllocDetail in);

    List<AllocDetailResult> toList(List<AllocDetail> in);

    @Mapping(target = "mchName", ignore = true)
    @Mapping(target = "appName", ignore = true)
    AllocOrderVo toVo(AllocOrder in);

    @Mapping(target = "mchName", ignore = true)
    @Mapping(target = "appName", ignore = true)
    @Mapping(target = "name", ignore = true)
    AllocDetailVo toVo(AllocDetail in);
}
