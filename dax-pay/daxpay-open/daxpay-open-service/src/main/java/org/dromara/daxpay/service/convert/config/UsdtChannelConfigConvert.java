package org.dromara.daxpay.service.convert.config;

import org.dromara.daxpay.service.entity.config.UsdtChannelConfig;
import org.dromara.daxpay.service.param.config.UsdtChannelConfigParam;
import org.dromara.daxpay.service.result.config.UsdtChannelConfigResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * USDT通道配置转换器
 * <AUTHOR>
 * @since 2024/7/17
 */
@Mapper
public interface UsdtChannelConfigConvert {
    UsdtChannelConfigConvert CONVERT = Mappers.getMapper(UsdtChannelConfigConvert.class);

    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "lastModifier", ignore = true)
    @Mapping(target = "lastModifiedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "mchNo", ignore = true)
    @Mapping(target = "appId", ignore = true)
    @Mapping(target = "nodeEndpoints", ignore = true)
    @Mapping(target = "confirmationBlocks", ignore = true)
    @Mapping(target = "minAmount", ignore = true)
    @Mapping(target = "maxAmount", ignore = true)
    @Mapping(target = "hotWalletAddress", ignore = true)
    @Mapping(target = "coldWalletAddress", ignore = true)
    @Mapping(target = "hotWalletThreshold", ignore = true)
    @Mapping(target = "encryptedMnemonic", ignore = true)
    @Mapping(target = "derivationIndex", ignore = true)
    @Mapping(target = "transMap", ignore = true)
    UsdtChannelConfig toEntity(UsdtChannelConfigParam param);

    @Mapping(target = "nodeUrl", ignore = true)
    @Mapping(target = "walletAddress", ignore = true)
    @Mapping(target = "privateKey", ignore = true)
    @Mapping(target = "gasPrice", ignore = true)
    @Mapping(target = "gasLimit", ignore = true)
    @Mapping(target = "confirmations", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "lastTestTime", ignore = true)
    @Mapping(target = "testStatus", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    UsdtChannelConfigResult toResult(UsdtChannelConfig entity);
}