server:
  port: 9999
spring:
  application:
    name: dax-pay-admin
  profiles:
    active: dev
  # 解决Spring Boot 3.x与MyBatis-Plus Join扩展兼容性问题
  sql:
    init:
      dependency-detection:
        enabled: false
  task:
    scheduling:
      pool:
        size: 8
  servlet:
    multipart:
      # 上传文件大小限制为100M
      max-file-size: 100MB
# ORM
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    banner: false
    db-config:
      # PG逻辑删除需要指定为布尔值, 如果为0/1会出现查询类型错误
      logic-delete-value: true
      logic-not-delete-value: false
mybatis-plus-join:
  banner: false
# 安全框架
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Accesstoken
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  active-timeout: -1
  is-concurrent: true
  is-share: true
  is-log: false
  is-print: false
# 字段翻译插件
easy-trans:
  #启用平铺模式
  is-enable-tile: true
# 平台配置
bootx-platform:
  config:
    deploy-mode: fusion
    client-codes:
      - dax-pay-admin
