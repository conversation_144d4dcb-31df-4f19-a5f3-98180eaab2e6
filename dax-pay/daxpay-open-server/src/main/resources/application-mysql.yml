spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          # MySQL连接
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************************************
          username: pay
          password: elt4RTckgPx.xgdg
      hikari:
        keepalive-time: 300000
        minimum-idle: 5
        maximum-pool-size: 20
        auto-commit: true
        idle-timeout: 30000
        pool-name: DaxPayHikariCP
        max-lifetime: 900000
        connection-timeout: 30000
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 0
      username: default
      password: "gyuan@lou"
      timeout: 10s
      lettuce:
        pool:
          max-wait: 1000ms
          max-active: 8
          max-idle: 8
          min-idle: 0
# 开发时显示debug日志
logging:
  level:
    cn.bootx.**: info
    cn.daxpay.**: info
    org.springframework.jdbc.core: debug
    root: info
  file:
    name: ./logs/daxpay.log
# 接口文档配置
springdoc:
  # 默认展开对象类型的属性, 主要用在get类型的参数中
  default-flat-param-object: true
# 基础脚手架配置
bootx-platform:
  common:
    # swagger相关配置
    swagger:
      author: sanfei
      title: 三飞科技支付平台
      description: 三飞科技支付平台
      version: 0.0.1
      base-packages:
        "[BootxPlatform接口]":
          - cn.bootx.platform.common
          - cn.bootx.platform.starter
          - cn.bootx.platform.iam
          - cn.bootx.platform.baseapi
          - cn.bootx.platform.notice
        "[支付平台接口]":
          - org.dromara.daxpay.controller
        "[支付通道接口]":
          - org.dromara.daxpay.channel
  starter:
    auth:
      enable-admin: true
      ignore-urls:
        - '/actuator/**'
        - '/v3/api-docs/**'
        - '/doc.html'
        - '/swagger-resources/**'
        - '/token/**'
        - '/ws/**'
        - '/demo/**'
        - '/test/**'
        - '/webjars/**'
        - '/front/**'
        - '/h5/**'
        - '/css/**'
        - '/error'
        - '/favicon.ico'
    file-upload:
      # 使用后端代理访问, 线上请使用 Nginx 配置或者直连方式，效率更高
      forward-server-url: http://127.0.0.1:9999
      file-server-url: http://127.0.0.1:9999
dax-pay:
  env: DEV_
  machine-no: 70
dromara:
  # 注意, 不要设置 domain 访问路径, 自行进行拼接访问路径, 来保证可迁移性
  x-file-storage:
    default-platform: local
    # 使用Nginx映射到存储路径, 然后将nginx的地址设置到 bootx-platform.starter.file-upload.file-server-url参数
    local-plus:
      - platform: local
        enable-storage: true
        base-path: /file/ # 基础路径
        storage-path: ./uploads # 存储路径 - 使用相对路径 