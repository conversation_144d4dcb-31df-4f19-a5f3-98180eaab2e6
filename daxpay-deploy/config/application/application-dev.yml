# DaxPay 开发环境配置
# 用于非打包模式下的开发调试

server:
  port: 9999
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  profiles:
    active: dev
  # 允许Bean定义覆盖，解决缓存管理器冲突
  main:
    allow-bean-definition-overriding: true
  
  # 数据源配置（动态数据源）
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          # MySQL连接
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************************************
          username: pay
          password: elt4RTckgPx.xgdg
      hikari:
        keepalive-time: 300000
        minimum-idle: 5
        maximum-pool-size: 20
        auto-commit: true
        idle-timeout: 30000
        pool-name: DaxPayHikariCP
        max-lifetime: 900000
        connection-timeout: 30000

  # Redis 配置
  redis:
    host: 127.0.0.1
    port: 6379
    username: default
    password: gyuan@lou
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      location: ./uploads

  # Jackson 配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# MyBatis Plus 配置
mybatis-plus:
  banner: false
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    banner: false
    db-config:
      # MySQL逻辑删除配置
      logic-delete-value: true
      logic-not-delete-value: false
      id-type: auto
  mapper-locations: classpath*:mapper/**/*Mapper.xml

# MyBatis Plus Join 配置
mybatis-plus-join:
  banner: false

# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO
    org.dromara.daxpay: DEBUG
    # 抑制不必要的日志
    org.mapstruct: WARN
    org.apache.maven: WARN
    org.springframework.boot.autoconfigure: WARN
    org.springframework.boot.context: WARN
    com.baomidou.mybatisplus: WARN
    com.alibaba.druid: WARN
    io.netty: WARN
    io.lettuce: WARN
    com.fasterxml.jackson: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    path: ./logs
    name: ./logs/daxpay.log
    max-size: 100MB
    max-history: 30

# DaxPay 配置
dax-pay:
  env: DEV_
  machine-no: 70

daxpay:
  # 开发模式
  dev-mode: true
  
  # 签名配置
  sign:
    # 开发环境可以关闭签名验证
    required: false
    secret: daxpay-dev-secret-key
  
  # 支付配置
  pay:
    # 支付超时时间（分钟）
    timeout: 30
    # 异步通知重试次数
    notify-retry: 3
    # 异步通知超时时间（秒）
    notify-timeout: 10
  
  # 支付宝配置（开发环境）
  alipay:
    # 沙箱环境
    sandbox: true
    app-id: 2021000122671080
    private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
    alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
    # 开发环境回调地址
    notify-url: http://localhost:9999/callback/alipay
    return-url: http://localhost:9999/return/alipay
  
  # 微信支付配置（开发环境）
  wechat:
    # 微信支付商户号
    mch-id: 1234567890
    # 微信支付API密钥
    api-key: your-wechat-api-key
    # 微信支付证书路径
    cert-path: classpath:cert/wechat/apiclient_cert.p12
    # 开发环境回调地址
    notify-url: http://localhost:9999/callback/wechat
  
  # USDT 配置（开发环境）
  usdt:
    # 测试网络配置
    networks:
      trc20:
        rpc-urls:
          - https://api.shasta.trongrid.io  # 测试网
        contract-address: TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs  # 测试网USDT合约
        confirmation-blocks: 1
      erc20:
        rpc-urls:
          - https://goerli.infura.io/v3/YOUR_PROJECT_ID  # 测试网
        contract-address: 0x509Ee0d083DdF8AC028f2a56731412edD63223B9  # 测试网USDT合约
        confirmation-blocks: 3

# 开发工具配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

# Swagger 配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: alpha
    tags-sorter: alpha
  group-configs:
    - group: 'daxpay-api'
      paths-to-match: '/api/**'
      packages-to-scan: org.dromara.daxpay.service.controller

# 开发环境特殊配置
debug: true

# 热部署配置（如果使用 spring-boot-devtools）
spring.devtools:
  restart:
    enabled: true
    additional-paths: src/main/java
  livereload:
    enabled: true

# 基础脚手架配置
bootx-platform:
  common:
    # swagger相关配置
    swagger:
      author: sanfei
      title: 三飞科技支付平台
      description: 三飞科技支付平台
      version: 0.0.1
      base-packages:
        "[BootxPlatform接口]":
          - cn.bootx.platform.common
          - cn.bootx.platform.starter
          - cn.bootx.platform.iam
          - cn.bootx.platform.baseapi
          - cn.bootx.platform.notice
        "[支付平台接口]":
          - org.dromara.daxpay.controller
        "[支付通道接口]":
          - org.dromara.daxpay.channel
  starter:
    auth:
      enable-admin: true
      ignore-urls:
        - '/actuator/**'
        - '/v3/api-docs/**'
        - '/doc.html'
        - '/swagger-resources/**'
        - '/token/**'
        - '/ws/**'
        - '/demo/**'
        - '/test/**'
        - '/webjars/**'
        - '/front/**'
        - '/h5/**'
        - '/css/**'
        - '/error'
        - '/favicon.ico'
    file-upload:
      # 使用后端代理访问, 线上请使用 Nginx 配置或者直连方式，效率更高
      forward-server-url: http://127.0.0.1:9999
      file-server-url: http://127.0.0.1:9999

# 文件存储配置
dromara:
  # 注意, 不要设置 domain 访问路径, 自行进行拼接访问路径, 来保证可迁移性
  x-file-storage:
    default-platform: local
    # 使用Nginx映射到存储路径, 然后将nginx的地址设置到 bootx-platform.starter.file-upload.file-server-url参数
    local-plus:
      - platform: local
        enable-storage: true
        base-path: /file/ # 基础路径
        storage-path: ./uploads # 存储路径 - 使用相对路径

# 安全框架配置
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Accesstoken
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  active-timeout: -1
  is-concurrent: true
  is-share: true
  is-log: false
  is-print: false

# 字段翻译插件
easy-trans:
  #启用平铺模式
  is-enable-tile: true
