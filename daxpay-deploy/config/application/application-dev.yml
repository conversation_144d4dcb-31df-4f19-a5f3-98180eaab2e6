# DaxPay 开发环境配置
# 用于非打包模式下的开发调试

server:
  port: 9999
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: pay
    password: elt4RTckgPx.xgdg
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DaxPayHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Redis 配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      location: ./uploads

  # <PERSON> 配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO
    org.dromara.daxpay: DEBUG
    # 抑制不必要的日志
    org.mapstruct: WARN
    org.apache.maven: WARN
    org.springframework.boot.autoconfigure: WARN
    org.springframework.boot.context: WARN
    com.baomidou.mybatisplus: WARN
    com.alibaba.druid: WARN
    io.netty: WARN
    io.lettuce: WARN
    com.fasterxml.jackson: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    path: ./logs
    name: ./logs/daxpay.log
    max-size: 100MB
    max-history: 30

# DaxPay 配置
daxpay:
  # 开发模式
  dev-mode: true
  
  # 签名配置
  sign:
    # 开发环境可以关闭签名验证
    required: false
    secret: daxpay-dev-secret-key
  
  # 支付配置
  pay:
    # 支付超时时间（分钟）
    timeout: 30
    # 异步通知重试次数
    notify-retry: 3
    # 异步通知超时时间（秒）
    notify-timeout: 10
  
  # 支付宝配置（开发环境）
  alipay:
    # 沙箱环境
    sandbox: true
    app-id: 2021000122671080
    private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
    alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
    # 开发环境回调地址
    notify-url: http://localhost:9999/callback/alipay
    return-url: http://localhost:9999/return/alipay
  
  # 微信支付配置（开发环境）
  wechat:
    # 微信支付商户号
    mch-id: 1234567890
    # 微信支付API密钥
    api-key: your-wechat-api-key
    # 微信支付证书路径
    cert-path: classpath:cert/wechat/apiclient_cert.p12
    # 开发环境回调地址
    notify-url: http://localhost:9999/callback/wechat
  
  # USDT 配置（开发环境）
  usdt:
    # 测试网络配置
    networks:
      trc20:
        rpc-urls:
          - https://api.shasta.trongrid.io  # 测试网
        contract-address: TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs  # 测试网USDT合约
        confirmation-blocks: 1
      erc20:
        rpc-urls:
          - https://goerli.infura.io/v3/YOUR_PROJECT_ID  # 测试网
        contract-address: 0x509Ee0d083DdF8AC028f2a56731412edD63223B9  # 测试网USDT合约
        confirmation-blocks: 3

# 开发工具配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

# Swagger 配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: alpha
    tags-sorter: alpha
  group-configs:
    - group: 'daxpay-api'
      paths-to-match: '/api/**'
      packages-to-scan: org.dromara.daxpay.service.controller

# 开发环境特殊配置
debug: true

# 热部署配置（如果使用 spring-boot-devtools）
spring.devtools:
  restart:
    enabled: true
    additional-paths: src/main/java
  livereload:
    enabled: true
