#!/bin/bash

# DaxPay 开发调试启动脚本
# 作者: DaxPay Team
# 版本: 2.0
# 描述: 非打包模式下的开发调试启动脚本

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"
PROJECT_ROOT="$(dirname "$DEPLOY_DIR")"
DAX_PAY_ROOT="$PROJECT_ROOT/dax-pay"
SERVICE_MODULE="$DAX_PAY_ROOT/daxpay-open-server"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
DaxPay 开发调试启动脚本使用说明

用法: $0 [选项]

启动模式:
  -m, --maven          使用 Maven 插件启动（默认）
  -c, --compile        编译后使用 java -cp 启动
  -d, --debug          启用远程调试模式
  -p, --profile PROF   指定环境配置 (dev/test，默认: dev)
  -P, --port PORT      指定服务端口 (默认: 9999)
  --debug-port PORT    指定调试端口 (默认: 5005)

其他选项:
  -s, --stop           停止开发服务
  --status             查看服务状态
  --logs               查看日志
  -v, --verbose        详细输出模式
  -h, --help           显示帮助信息

环境变量:
  JAVA_OPTS           Java 启动参数
  MAVEN_OPTS          Maven 启动参数
  DEV_CONFIG_PATH     开发配置文件路径

示例:
  $0                          Maven 插件启动
  $0 -d                       启用调试模式启动
  $0 -c -p test              编译模式启动，使用test环境
  $0 -m -P 8080 -d           Maven启动，端口8080，启用调试
  $0 --stop                   停止开发服务

EOF
}

# 检查环境
check_environment() {
    log_info "检查开发环境..."

    # 检查 Java
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装或不在 PATH 中"
        exit 1
    fi

    local java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2)
    log_info "Java 版本: $java_version"

    # 检查 Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装或不在 PATH 中"
        exit 1
    fi

    local maven_version=$(mvn -version | head -n1 | cut -d' ' -f3)
    log_info "Maven 版本: $maven_version"

    # 检查项目目录
    if [ ! -d "$SERVICE_MODULE" ]; then
        log_error "服务模块目录不存在: $SERVICE_MODULE"
        exit 1
    fi

    if [ ! -f "$SERVICE_MODULE/pom.xml" ]; then
        log_error "服务模块 POM 文件不存在"
        exit 1
    fi

    log_info "环境检查完成"
}

# 检查并安装项目依赖
check_and_install_dependencies() {
    log_info "检查项目依赖..."

    cd "$DAX_PAY_ROOT"

    # 检查本地仓库中是否存在项目依赖
    local local_repo=$(mvn help:evaluate -Dexpression=settings.localRepository -q -DforceStdout)
    local required_artifacts=(
        "daxpay-open-core"
        "daxpay-open-service"
        "daxpay-open-controller"
        "daxpay-open-channel-alipay"
        "daxpay-open-channel-wechat"
        "daxpay-open-channel-union"
        "daxpay-open-channel-usdt"
    )

    local missing_artifacts=()
    for artifact in "${required_artifacts[@]}"; do
        local artifact_path="$local_repo/org/dromara/daxpay/$artifact/3.0.13/$artifact-3.0.13.jar"
        if [ ! -f "$artifact_path" ]; then
            missing_artifacts+=("$artifact")
        fi
    done

    if [ ${#missing_artifacts[@]} -gt 0 ]; then
        log_info "项目依赖不完整，缺少: ${missing_artifacts[*]}"
        log_info "开始安装到本地仓库..."
        mvn clean install -DskipTests -Dmaven.test.skip=true -Dgpg.skip=true -q

        if [ $? -ne 0 ]; then
            log_error "项目依赖安装失败"
            log_error "请检查项目是否能正常编译"
            exit 1
        fi

        log_success "项目依赖安装完成"
    else
        log_info "项目依赖已存在，跳过安装"
    fi
}

# 获取进程 PID
get_dev_pid() {
    # 查找 DaxpayOpenServiceApplication 进程
    ps aux | grep "DaxpayOpenServiceApplication" | grep -v grep | awk '{print $2}' | head -1
}

# 检查服务状态
check_dev_status() {
    local pid=$(get_dev_pid)
    if [ -n "$pid" ]; then
        echo "running"
    else
        echo "stopped"
    fi
}

# Maven 插件启动
start_with_maven() {
    log_info "使用 Maven 插件启动开发服务..."

    # 检查并安装项目依赖
    check_and_install_dependencies

    cd "$SERVICE_MODULE"
    
    # 构建 Maven 命令
    local mvn_cmd="mvn spring-boot:run"

    # 添加编译器参数抑制警告
    mvn_cmd="$mvn_cmd -Dmaven.compiler.showWarnings=false"
    mvn_cmd="$mvn_cmd -Dmaven.compiler.showDeprecation=false"
    mvn_cmd="$mvn_cmd -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn"

    # 添加环境配置
    mvn_cmd="$mvn_cmd -Dspring-boot.run.profiles=$PROFILE"
    
    # 添加端口配置
    local run_args="--server.port=$PORT"
    
    # 添加日志配置
    run_args="$run_args --logging.file.path=$PROJECT_ROOT/logs"
    run_args="$run_args --spring.servlet.multipart.location=$PROJECT_ROOT/uploads"
    
    # 添加外部配置文件
    if [ -n "$DEV_CONFIG_PATH" ] && [ -f "$DEV_CONFIG_PATH" ]; then
        run_args="$run_args --spring.config.location=classpath:/,file:$DEV_CONFIG_PATH"
    fi
    
    mvn_cmd="$mvn_cmd -Dspring-boot.run.arguments=\"$run_args\""
    
    # 添加 JVM 参数
    local jvm_args="$JAVA_OPTS"
    
    # 调试模式
    if [ "$DEBUG_MODE" = "true" ]; then
        jvm_args="$jvm_args -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=$DEBUG_PORT"
        log_info "调试端口: $DEBUG_PORT"
    fi
    
    if [ -n "$jvm_args" ]; then
        mvn_cmd="$mvn_cmd -Dspring-boot.run.jvmArguments=\"$jvm_args\""
    fi
    
    log_info "启动命令: $mvn_cmd"
    log_info "服务端口: $PORT"
    log_info "环境配置: $PROFILE"
    
    # 执行启动
    eval $mvn_cmd
}

# 编译模式启动
start_with_compile() {
    log_info "使用编译模式启动开发服务..."

    # 检查并安装项目依赖
    check_and_install_dependencies

    cd "$DAX_PAY_ROOT"

    # 编译项目
    log_info "编译项目..."
    mvn compile -q
    
    if [ $? -ne 0 ]; then
        log_error "项目编译失败"
        exit 1
    fi
    
    # 构建 classpath
    log_info "构建 classpath..."
    local classpath=$(mvn -pl daxpay-open-service dependency:build-classpath -Dmdep.outputFile=/tmp/cp.txt -q && cat /tmp/cp.txt)
    classpath="$SERVICE_MODULE/target/classes:$classpath"
    
    # 构建启动命令
    local java_cmd="java"
    
    # 添加 JVM 参数
    if [ -n "$JAVA_OPTS" ]; then
        java_cmd="$java_cmd $JAVA_OPTS"
    fi
    
    # 调试模式
    if [ "$DEBUG_MODE" = "true" ]; then
        java_cmd="$java_cmd -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=$DEBUG_PORT"
        log_info "调试端口: $DEBUG_PORT"
    fi
    
    # 添加 classpath 和主类
    java_cmd="$java_cmd -cp $classpath org.dromara.daxpay.service.DaxpayOpenServiceApplication"
    
    # 添加应用参数
    java_cmd="$java_cmd --spring.profiles.active=$PROFILE"
    java_cmd="$java_cmd --server.port=$PORT"
    java_cmd="$java_cmd --logging.file.path=$PROJECT_ROOT/logs"
    java_cmd="$java_cmd --spring.servlet.multipart.location=$PROJECT_ROOT/uploads"
    
    # 添加外部配置文件
    if [ -n "$DEV_CONFIG_PATH" ] && [ -f "$DEV_CONFIG_PATH" ]; then
        java_cmd="$java_cmd --spring.config.location=classpath:/,file:$DEV_CONFIG_PATH"
    fi
    
    log_info "启动命令: $java_cmd"
    log_info "服务端口: $PORT"
    log_info "环境配置: $PROFILE"
    
    # 执行启动
    eval $java_cmd
}

# 停止开发服务
stop_dev_service() {
    log_info "停止开发服务..."
    
    local pid=$(get_dev_pid)
    if [ -z "$pid" ]; then
        log_warn "开发服务未运行"
        return 0
    fi
    
    log_info "正在停止开发服务 (PID: $pid)..."
    kill $pid
    
    # 等待进程结束
    local count=0
    while [ $count -lt 15 ]; do
        if ! ps -p $pid > /dev/null 2>&1; then
            break
        fi
        sleep 1
        count=$((count + 1))
    done
    
    if ps -p $pid > /dev/null 2>&1; then
        log_warn "服务未能正常停止，强制终止..."
        kill -9 $pid
    fi
    
    log_info "开发服务已停止"
}

# 查看服务状态
show_dev_status() {
    local status=$(check_dev_status)
    local pid=$(get_dev_pid)
    
    if [ "$status" = "running" ]; then
        log_info "DaxPay 开发服务正在运行 (PID: $pid)"
        
        # 显示端口信息
        local port_info=$(netstat -tlnp 2>/dev/null | grep ":$PORT " | head -1)
        if [ -n "$port_info" ]; then
            log_info "服务端口: $PORT (正在监听)"
        else
            log_warn "服务端口: $PORT (未监听，可能正在启动中)"
        fi
        
        # 显示内存使用
        local memory=$(ps -p $pid -o rss= 2>/dev/null | awk '{print int($1/1024)"MB"}')
        if [ -n "$memory" ]; then
            log_info "内存使用: $memory"
        fi
    else
        log_warn "DaxPay 开发服务未运行"
    fi
}

# 查看日志
show_dev_logs() {
    local log_file="$PROJECT_ROOT/logs/daxpay.log"
    
    if [ ! -f "$log_file" ]; then
        log_warn "日志文件不存在: $log_file"
        log_info "请检查服务是否已启动"
        return 1
    fi
    
    log_info "显示开发日志: $log_file"
    tail -f "$log_file"
}

# 初始化配置
init_config() {
    # 默认配置
    START_MODE="${START_MODE:-maven}"
    PROFILE="${PROFILE:-dev}"
    PORT="${PORT:-9999}"
    DEBUG_MODE="${DEBUG_MODE:-false}"
    DEBUG_PORT="${DEBUG_PORT:-5005}"
    
    # 环境变量配置
    JAVA_OPTS="${JAVA_OPTS:--Xms512m -Xmx1g -XX:+UseG1GC}"
    MAVEN_OPTS="${MAVEN_OPTS:--Xms256m -Xmx512m}"
    
    # 设置开发配置文件路径
    if [ -z "$DEV_CONFIG_PATH" ]; then
        DEV_CONFIG_PATH="$DEPLOY_DIR/config/application/application-dev.yml"
    fi
    
    # 创建必要的目录
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/uploads"
    mkdir -p "$PROJECT_ROOT/config"
    
    log_info "DaxPay 开发启动配置:"
    log_info "  启动模式: $START_MODE"
    log_info "  环境配置: $PROFILE"
    log_info "  服务端口: $PORT"
    log_info "  调试模式: $DEBUG_MODE"
    if [ "$DEBUG_MODE" = "true" ]; then
        log_info "  调试端口: $DEBUG_PORT"
    fi
    log_info "  Java 参数: $JAVA_OPTS"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--maven)
            START_MODE="maven"
            shift
            ;;
        -c|--compile)
            START_MODE="compile"
            shift
            ;;
        -d|--debug)
            DEBUG_MODE=true
            shift
            ;;
        -p|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -P|--port)
            PORT="$2"
            shift 2
            ;;
        --debug-port)
            DEBUG_PORT="$2"
            shift 2
            ;;
        -s|--stop)
            ACTION="stop"
            shift
            ;;
        --status)
            ACTION="status"
            shift
            ;;
        --logs)
            ACTION="logs"
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 设置详细输出
if [ "$VERBOSE" = "true" ]; then
    set -x
fi

# 初始化配置
init_config

# 检查环境
check_environment

# 执行操作
case "${ACTION:-start}" in
    start)
        case "$START_MODE" in
            maven)
                start_with_maven
                ;;
            compile)
                start_with_compile
                ;;
            *)
                log_error "未知启动模式: $START_MODE"
                exit 1
                ;;
        esac
        ;;
    stop)
        stop_dev_service
        ;;
    status)
        show_dev_status
        ;;
    logs)
        show_dev_logs
        ;;
    *)
        log_error "未知操作: ${ACTION:-start}"
        show_help
        exit 1
        ;;
esac
