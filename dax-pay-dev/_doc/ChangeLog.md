# CHANGELOG
## [v3.0.0.beta5] 2025-05-01

## [v3.0.0.beta4] 2025-01-10
- 新增: 微信服务商支付支持
- 新增: 支付宝服务商支付支持
- 新增: 系统首页驾驶舱数据展示页we
- 新增: 微信支持公钥证书方式
- 优化: 商户应用增加停用功能
- 优化: 对各种交易增加新的同步失败异常处理, 防止同步失败后无限进行同步
- fix: 微信支付同步V3接口金额空指针问题
- fix: 订单支付成功重复更新问题
- fix: 优化自动分账没有默认分账组时的处理
## [v3.0.0.beta3] 2024-12-22
- 新增: 支持支付宝分账功能
- 新增: 支持微信分账功能
- 新增: 支持分账发起和分账完结功能
- 新增: 支持分账接收方配置功能
- 新增: 支持支付订单自动分账功能
- 新增: 自动同步分账订单状态功能
- 新增: 分账回调通知和分账消息通知功能
- 新增: 自动完结分账订单功能
- 新增: 分账同步功能
- 优化: 升级wxjava4.6.9.B，并处理证书配置问题
- 优化: 调整分账接收者和分账组配置逻辑
- fix: 修复微信v2当面付发起失败问题
- fix: 修复微信v2分账参数未设置问题
- fix: sm3签名校验问题
- fix: SDK参数和返回对象与接口不一致修改

## [v3.0.0.beta2] 2024-12-05
- 新增: 增加PC收银台功能
- 新增: 增加H5收银台功能
- 新增: 增加聚合收银台功能
- 新增: 增加收银台配置功能
- 新增: 交易调试页增加收银台调试选项
- 新增: SDK增加收银台相关接口/认证相关接口
- 重构: 原支付收银台改为支付码牌, 一个应用支持多个码牌
- 重构: SDK命名空间更改为org.dromara.daxpay
- 优化: 微信通道添加单独的认证跳转地址
- 优化: 添加定时任务和事件监听服务
- 优化: 微信支付方式的判断逻辑，提高了系统稳定性
- fix: 系统参数使用到MySQL8保留字
- fix: Mysql 脚本缺少 缺失 表pay_api_const
- fix: H5构建版本限制错误, 限制为最低为node20+
- fix: 修复商户回调和通知的延迟逻辑
- fix: 商户应用类型命名错误
- fix: 修复对账差异逻辑
- fix: 修复微信支付同步金额为空的问题
- fix: 修复 BigDecimal 类型数据序列化和签名异常问题
## [v3.0.0.beta1] 2024-10-24
- 重构: JDK版本升级为21+, Spring Boot 版本升级为3.3.x, 前端组件升级为Antd Vue 4.x + Vite5
- 重构: 数据库更新为PostgreSQL + MySQL8.x 双版本支持
- 重构: 脚手架全新重构, 精简和优化各种功能模块, 支持基于有赞文章实现的Redis延时队列
- 重构: 支持多应用模式, 每个应用都可以配置单独一套支付通道、通知订阅、收款码牌等配置, 可以实现同时对接多个业务系统
- 重构: 项目结构进行重构, 修改为支付核心+通道扩展+功能插件的方式, 实现功能模块的耦合拆分, 便于进行功能扩展和二次开发
- 重构: 对账功能进行重构, 更加简单直观和易用
- 重构: 删除订单调整相关逻辑, 分别放到订单同步和回调处理中, 只保留
- 新增: 微信支付同时支持V2和V3版本的接口, 同时V3版本支持付款码和撤销接口
- 新增: 交易调试接口功能, 用于开发时对交易流程进行测试
- 新增: 获取通道认证信息的测试页, 便于获取微信、支付宝等用户的认证信息
- 新增: 增加简易移动端收款码牌功能, 支持自动跳转到微信或支付宝对应的H5收银台, 支持自主配置所使用的通道和支付方式
- 新增: 增加商户通知功能, 通过订阅指定类型的通知类型, 将会在符合条件时推送到预留的客户系统地址上
- 优化: 各类错误处理进行统一化处理
- 优化: 优化商户回调功能, 简化配置项, 使用延时器优化重复推送的逻辑
- 优化: 减少在各种流程中上下文对象的线程变量使用, 非必需的上下文对象使用方法调用明确传输
- 优化: 对各类状态码进行优化合并, 如转账接收方类型、分账接收方类型等
- 优化: 所有金额统一为元, 保留两位小数
## [v2.0.8] 2024-06-27
- 新增: 撤销接口
- 新增: 转账功能
- 新增: DEMO增加转账演示功能
- 新增: DEMO增加获取OpenID功能
- 新增: 支付宝支持JSAPI方式支付
- 新增: 绑定对账接收方增加扫码获取微信OpenID和支付宝OpenId功能
- 新增: 支付宝微信等消息通知地址支持一键生成
- 新增: 请求IP参数增加正则校验
- 优化: 手动发起分账重试参数修正
- 优化: 细分各种支付异常类和编码
- 优化: 支付宝SDK修改为官方SDK
- 优化: 界面金额统一调整为元
- 优化: 上下文对象进行优化精简
- 优化: 支付接口公共参数添加随机数字段, 预防重放问题
- 优化: 请求接口增加有效期校验, 超时后失效
- 优化: 数据库表进行规则, 字段设置长度, 增加索引, 对应请求参数添加校验
- 优化: 订单和扩展信息进行合并
- 优化: 支付通道两个独立的配置进行合并为一个
- 优化: 平台配置增加接口请求有效时长配置
- 优化: 平台配置和接口配置删除回调地址配置
- 优化: 接口配置删除是否验签配置和回调地址
- 优化: 分账订单相关命名统一为Alloc
- 优化: 支付订单拆分退款状态为单独的字段
- 优化: 策略工厂修改为统一的通用策略工厂
- 优化: 支付和退款达到终态不可以再回退回之前的状态
- 优化: 优化认证授权地址配置, 拆分为支持单独配置
- 优化: 优化各类网址配置, 兼容结尾带/和不带/
- fix: 修复支付关闭参数名称不正确问题
- fix: 退款回调消息字段不一致导致验签不通过问题
- fix: 云闪付空指针问题

## [v2.0.7] 2024-06-05
- 新增: 资金流水记录功能
- 新增: 分账功能支持分账组分账和自己传接收方进行分账
- 新增: 分账接收的添加、删除、查询接口调用
- 新增: 分账发起、完结、同步功能支持接口调用
- 新增: 支持自动分账和手动发起分账两种
- 新增: 分账通知发送功能
- 优化: 对超时订单进行处理(数据库定时同步)
- 优化: 订单金额小于0.01元直接忽略不进行分账,增加新状态,
- 优化: 优化签名注解和上下文初始化注解切面
- 优化: 分账重试会自动根据分账失败和
- 优化: 优化签名注解和上下文初始化注解切面, 更方便初始化上下文
- fix: 对账差异单数据不一致处理异常, 本地待对账订单类型记录错误
- fix: 订单超时任务注册任务错误，id改为订单号
- fix: 系统中金额分转元精度异常问题
- fix: 同步回调处理参数订单号接收失败
- fix: 支付和退款消息签名值不一致问题
- fix: 分账发起时错误的使用订单号作为分账号

## [v2.0.6] 2024-05-15
- 新增: 下载原始对账单功能，转换为指定格式进行下载
- 新增: 增加对账结果计算和显示，以及对单差异数据查看功能
- 新增: 自动分账功能，支付完成后自动根据默认分账组将订单分账
- 新增: 三方支付通道订单号规则优化: 支付P、退款R、分账A，可以根据环境加前缀：DEV_、DEMO_、PRE_
- 优化: 去除组合支付概念，删除现金支付和储值卡支付方式，系统整体复杂度降低一半以上
- 优化: 消息通知发送流程改造，不在使用复杂继承组合关系，只保留一级类继承关系
- 优化: 回调通知处理不再使用继承模式，修改为组合模式，提高阅读和debug的便利性
- 优化: 支付同步、回调和退款同步、回调去除组合支付导致的特殊处理逻辑
- 优化: 统一公共请求参数和响应参数，同时响应参数格式，便于进行统一处理
- 优化: 统一参数命名规则，包括支付、退款、对账、分账等相关参数的属性，实现风格的统一
- 优化: 使用切面统一处理API调用异常, 做统一包装返回
- 优化: 金额显示统一使用元
- 优化: 前端查询条件适配，统一页面交互逻辑，初步完成管理端的功能完备性
- 优化: 支持自动同步对账结果，并自动对分账单进行完结
- 优化: 基础脚手架从jar集成修改为源码集成
- fix: 自动同步任务不生效
- fix: 算收款金额时对产生退款的支付订单未进行计算
## [v2.0.5] 2024-04-18
- 新增: 支持支付宝分账功能
- 新增: 支持微信分账功能
- 新增: 分账接收者和分账组管理
- 新增: 支持分账结果同步功能
- 新增: 支付通道配置中支持是否支持分账
- 新增: SDK支持分账接口
- 优化: 收银台演示支持设置是否分账
- fix: 修复创建支付订单报错时, 订单保存数据不完整
## [v2.0.4] 2024-03-26
- 新增: 首页驾驶舱功能: 各通道收入和支付情况
- 新增: 云闪付支持对账功能
- 新增: 对账文件支持手动导入
- 新增: 结算台DEMO增加云闪付示例
- 新增: 增加支付限额，包括整单限额和通道限额
- 优化: 支付流程也改为先落库后支付情况, 避免极端情况导致掉单
- 优化: 前端列表状态显示优化
- fix: 对账订单流水号生成规则不是按天生成修改

## [v2.0.3] 2024-03-16
- 增加云闪付通道，支持支付、退款、同步、回调处理
- 增加定时同步退款中的退款订单任务
- 增加通知任务订单的状态类型，例如订单关闭、成功、失败等
- 增加退款操作支持重试
- 增加手动触发通知任务消息的发送功能

##  [v2.0.2] 2024-03-06
- 增加微信支付对账功能
- 增加支付宝支付对账功能
- 优化: 修复策略对订单时间和状态字段的变更优化
- fix: 前端支付订单查询条件中"支付ID"条件不生效

##  [v2.0.1] 2024-02-27
- 增加支付、退款时客户通知功能，支持多次重发
- 开源文档增加支付通知和退款通知文档
- 增加客户通知任务记录功能
- 支持钱包支付、流水记录、各类操作等功能
- 支持储值卡支付、流水记录、各类操作等功能
- 支持现金支付和流水记录功能
- 增加支付宝流水记录功能
- 增加微信流水记录功能
- 变更: 废弃调用接口时的`version`字段，调用时不再进行传输，SDK中同步进行删除
- 优化: 订单支持关闭时间记录
- 优化: 增加退款订单扩展记录
- 优化: SDK增加简单退款、多通道退款等多中测试样例
- 优化: IJPay进行Https请求时, TLS版本使用读取JDK中支持的版本
- fix: 同步支付通道订单不能正确生成
- fix: 修复聚合条码支付时付款码未传输问题
- fix: 修复微信退款同步时, 错误信息未保存问题
- fix: 修复手动发起退款时上下文未进行初始化的问题
- fix: 修复简单退款选择全部退款时报错问题
- fix: 修复退款时未检验退款金额问题，导致可以退款余额可以大于可退余额

##  [v2.0.0] 2024-02-14
- 支持支付宝支付: 扫码支付、付款码支付、PC支付、H5支付
- 支持微信支付: 扫码支付、WAP支付、公众号支付
- 增加聚合支付演示功能，支持支付宝和微信支付
- 增加PC收银台演示功能，各种类型的支付
- 增加手机收银台演示功能，支持在微信、支付宝、浏览器中发起对应的请求
- 提供Java版本SDK，简化业务系统对支付网关的调用
- 支持请求参数签名和验签机制，已经支持SHA256和MD5
- 支持支付订单超时自动进行关闭
- 支持支付订单手动关闭功能
- 支持支付退款功能，可以进行全部退款或部分退款
- 支持支付同步功能，通过同步接口可以获取第三方支付网关的状态
- 支持支付和退款订单的修复功能，根据取第三方支付网关订单的状态，对订单进行修正，如支付同步、退款同步、消息回调等可触发
- 部分支付对账功能，已经实现支付宝和微信对账单下载解析和保存的功能
- 支持对各支付通道进行管理，包括是否启用、显示Logo图等
- 支持对支付网关对外暴露的接口进行管理，支持启停用、是否验签、是否消息通知等功能
- 去除调用时的用户概念，作为独立的支付网关使用
- 组合支付已预先进行支持，支持一个异步支付+多个同步支付通道组合进行收单支付
- 记录支付时出现的回调记录、同步记录、修复记录、关闭记录
