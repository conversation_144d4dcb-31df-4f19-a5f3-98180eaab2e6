<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.bootx.platform</groupId>
        <artifactId>bootx-platform-common</artifactId>
        <version>3.0.0.beta5</version>
    </parent>

    <artifactId>common-header-holder</artifactId>
    <description>请求头工具</description>

<dependencies>
    <!-- Spring封装 -->
    <dependency>
        <groupId>cn.bootx.platform</groupId>
        <artifactId>common-spring</artifactId>
        <version>${bootx-platform.version}</version>
    </dependency>


    </dependencies>

</project>
