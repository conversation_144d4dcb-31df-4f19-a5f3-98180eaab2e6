<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.bootx.platform</groupId>
        <artifactId>bootx-platform-common</artifactId>
        <version>3.0.0.beta5</version>
    </parent>

    <artifactId>common-mybatis-plus</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <!-- spring jdbc -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <!-- MP -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <!-- jdk 11+ 引入可选模块 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
        </dependency>
        <!-- MPJ -->
        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join-boot-starter</artifactId>
            <version>${mybatis-plus-join.version}</version>
        </dependency>
        <!-- 多数据源 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
            <version>${dynamic-datasource.version}</version>
        </dependency>
        <!-- 依赖缓存 -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>${caffeine.version}</version>
        </dependency>
        <!-- 翻译组件 -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>easy-trans-spring-boot-starter</artifactId>
            <version>${easytrans.version}</version>
        </dependency>
        <!-- 翻译组件 MP扩展 -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>easy-trans-mybatis-plus-extend</artifactId>
            <version>${easytrans.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bootx.platform</groupId>
            <artifactId>common-spring</artifactId>
            <version>${bootx-platform.version}</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>${mybatis-plus.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
