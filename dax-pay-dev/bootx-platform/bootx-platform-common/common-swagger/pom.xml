<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.bootx.platform</groupId>
        <artifactId>bootx-platform-common</artifactId>
        <version>3.0.0.beta5</version>
    </parent>

    <artifactId>common-swagger</artifactId>
    <packaging>jar</packaging>
    <description>接口文档</description>

    <dependencies>
        <!-- 官方4.5.0 与 最新Spring boot 3.4.x不兼容, 暂时使用第三方的包 -->
        <dependency>
            <groupId>com.github.xingfudeshi</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
    </dependencies>

</project>
