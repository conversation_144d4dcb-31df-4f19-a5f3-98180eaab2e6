<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>cn.bootx.platform</groupId>
        <artifactId>bootx-platform</artifactId>
        <version>3.0.0.beta5</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>bootx-platform-common</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>common-swagger</module>
        <module>common-mybatis-plus</module>
        <module>common-jackson</module>
        <module>common-spring</module>
        <module>common-exception-handler</module>
        <module>common-config</module>
        <module>common-header-holder</module>
        <module>common-redis</module>
        <module>common-log</module>
    </modules>

    <dependencies>
        <!-- 核心包 -->
        <dependency>
            <groupId>cn.bootx.platform</groupId>
            <artifactId>bootx-platform-core</artifactId>
            <version>${bootx-platform.version}</version>
        </dependency>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
