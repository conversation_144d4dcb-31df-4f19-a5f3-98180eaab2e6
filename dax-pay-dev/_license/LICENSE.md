# 借鉴和部分参考的开源项目

Spring Cloud Gateway整合Swagger2 Demo，全网首例： 
https://gitee.com/wxdfun/sw

JEECG BOOT 低代码开发平台： 
https://github.com/jeecgboot/jeecg-boot

HZERO-基于微服务架构开源免费的企业级PaaS平台：
https://gitee.com/open-hand/hzero

RuoYi-Vue 全新 Pro 版本： 
https://gitee.com/zhijiantianya/ruoyi-vue-pro

Snowy国产密码算法后台权限管理系统： 
https://gitee.com/xiaonuobase/snowy

表单设计器 k-form-design： 
https://gitee.com/kcz66/k-form-design

Vue微信菜单编辑器: 
https://github.com/hopex/vue-menu

flowable antd vue 的工作流设计器:
https://gitee.com/Vincent-Vic/workflow-bpmn-modeler-antdv

flowable 工作流相关思路和实现 乐之终曲:
https://blog.csdn.net/qq_37143673

GoView 一个Vue3搭建的低代码数据可视化开发平台:
https://gitee.com/dromara/go-view

easy-cron 这是基于Vue.js和iviewui封装一个crontab表达式的组件:
https://gitee.com/toktok/easy-cron

ACTable是对Mybatis做的增强功能，通过配置model注解的方式来创建表，修改表结构，并且实现了共通的CUDR功能提升开发效率：
https://gitee.com/sunchenbin/mybatis-enhance

Knife4j是一个集Swagger2 和 OpenAPI3为一体的增强解决方案：
https://gitee.com/xiaoym/knife4j

easy_trans 一个注解搞定数据翻译：
https://gitee.com/dromara/easy_trans

vue3-vant4-mobile Vant4脚手架:
https://github.com/xiangshu233/vue3-vant4-mobile

全能第三方支付对接Java开发工具包: 
https://gitee.com/egzosn/pay-java-parent
